import Constants from 'expo-constants';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define base types for API responses
interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
}

// Error class for API errors
export class ApiError extends Error {
  status: number;
  data?: any;

  constructor(message: string, status: number, data?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

class ApiService {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;
  private readonly API_URL_STORAGE_KEY = 'app_api_url';
  private readonly DEFAULT_API_URL = 'http://192.168.166.95:4000';

  constructor() {
    // Initialize with default, will be updated in init()
    this.baseUrl = this.DEFAULT_API_URL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    
    // Initialize the API URL
    this.init();
  }

  // Initialize API URL from storage or environment
  private async init() {
    try {
      // Try to get from AsyncStorage first
      const storedUrl = await AsyncStorage.getItem(this.API_URL_STORAGE_KEY);
      
      if (storedUrl) {
        this.baseUrl = storedUrl;
      } else {
        // Fall back to environment variable or default
        this.baseUrl = Constants.expoConfig?.extra?.apiUrl || this.DEFAULT_API_URL;
        // Save the default to storage for future use
        await this.setApiUrl(this.baseUrl);
      }
    } catch (error) {
      console.error('Failed to initialize API URL:', error);
      // Fall back to default if there's an error
      this.baseUrl = this.DEFAULT_API_URL;
    }
  }

  // Method to update the API URL
  async setApiUrl(url: string): Promise<void> {
    try {
      await AsyncStorage.setItem(this.API_URL_STORAGE_KEY, url);
      this.baseUrl = url;
    } catch (error) {
      console.error('Failed to save API URL:', error);
      throw new Error('Failed to save API URL');
    }
  }

  // Get the current API URL
  getApiUrl(): string {
    return this.baseUrl;
  }

  // Add auth token to requests
  setAuthToken(token: string | null) {
    if (token) {
      this.defaultHeaders['Authorization'] = `Bearer ${token}`;
    } else {
      delete this.defaultHeaders['Authorization'];
    }
  }

  // Generic request method
  private async request<T>(
    endpoint: string,
    method: string,
    data?: any,
    customHeaders?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const headers = {
      ...this.defaultHeaders,
      ...customHeaders,
    };

    const config: RequestInit = {
      method,
      headers,
      body: data ? JSON.stringify(data) : undefined,
    };

    try {
      const response = await fetch(url, config);
      const responseData = await response.json();
      
      if (!response.ok) {
        throw new ApiError(
          responseData.message || 'An error occurred',
          response.status,
          responseData
        );
      }

      return {
        data: responseData,
        status: response.status,
        message: responseData.message,
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError(
        error instanceof Error ? error.message : 'Network error',
        500
      );
    }
  }

  // HTTP method wrappers
  async get<T>(endpoint: string, customHeaders?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'GET', undefined, customHeaders);
  }

  async post<T>(endpoint: string, data?: any, customHeaders?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'POST', data, customHeaders);
  }

  async put<T>(endpoint: string, data?: any, customHeaders?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'PUT', data, customHeaders);
  }

  async patch<T>(endpoint: string, data?: any, customHeaders?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'PATCH', data, customHeaders);
  }

  async delete<T>(endpoint: string, customHeaders?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'DELETE', undefined, customHeaders);
  }
}

// Export a singleton instance
export const api = new ApiService();
