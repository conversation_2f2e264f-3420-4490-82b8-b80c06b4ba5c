// import { Redirect } from 'expo-router';

// export default function RootIndex() {
//   return <Redirect href="/(drawer)" />;
// }
import { Image } from "expo-image";
import { StyleSheet, Button } from "react-native";
import { useRouter } from "expo-router";
import { rtlStyle } from "@/utils/rtl";

import { HelloWave } from "@/components/HelloWave";
import ParallaxScrollView from "@/components/ParallaxScrollView";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Card } from "@/components/Card";

export default function HomeScreen() {
  const router = useRouter();
  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: "#A1CEDC", dark: "#1D3D47" }}
      headerImage={
        <Image
          source={require("@/assets/images/college-entrance-exam-concept-illustration.jpg")}
          // source={require("@/assets/images/LOGO-400x400.png")}
          style={styles.headerLogo}
        />
      }
    >
      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title">برنامه مدیریت آزمون</ThemedText>
        <HelloWave />
      </ThemedView>
      <ThemedView style={styles.stepContainer}>
        <Card style={styles.heroCard}>
          <ThemedText style={styles.heroCardTitle}>
            آماده‌ی چالش هستی؟
          </ThemedText>
          <ThemedText style={styles.heroCardSubtitle}>
            یک آزمون جدید را برای سنجش دانش خود شروع کنید.
          </ThemedText>
          <Button
            title="شروع آزمون جدید"
            onPress={() => router.navigate("/screens/ExamsTab")}
            style={{ backgroundColor: "#fff", marginTop: 20 }}
            textStyle={{ color: "#4F46E5" }}
          />
        </Card>

        <ThemedText type="subtitle">مدیریت آزمون‌ها</ThemedText>
        <Button
          title="ایجاد آزمون جدید"
          onPress={() => router.push("/screens/ExamAdd")}
        />
        <Button
          title="آزمون‌ها"
          onPress={() => router.push("/screens/ExamsList")}
        />
        <Button
          title="جلسه‌ها"
          onPress={() => router.push("/screens/SessionsList")}
        />
      </ThemedView>
      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle"></ThemedText>
        <ThemedText></ThemedText>
      </ThemedView>
    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: "center",
    gap: 8,
    fontFamily: "Vazirmatn",
  },
  stepContainer: {
    gap: 8,
    marginBottom: 8,
  },
  headerLogo: {
    height: 250,
    width: "100%",
    bottom: 0,
    ...rtlStyle.left(0),
    position: "absolute",
  },
});
