import { Image } from 'expo-image';
import { Platform, StyleSheet } from 'react-native';

import { Collapsible } from '@/components/Collapsible';
import { ExternalLink } from '@/components/ExternalLink';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';

export default function ExploreScreen() {
  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#D0D0D0', dark: '#353636' }}
      headerImage={
        <IconSymbol
          size={310}
          color="#808080"
          name="chevron.left.forwardslash.chevron.right"
          style={styles.headerImage}
        />
      }>
      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title">کاوش!!</ThemedText>
      </ThemedView>
      <ThemedText>این برنامه شامل کدهای نمونه برای شروع کار شماست.</ThemedText>
      <Collapsible title="مسیریابی مبتنی بر فایل">
        <ThemedText>
          این برنامه دارای دو صفحه است:{' '}
          <ThemedText type="defaultSemiBold">app/(tabs)/index.tsx</ThemedText> و{' '}
          <ThemedText type="defaultSemiBold">app/(tabs)/explore.tsx</ThemedText>
        </ThemedText>
        <ThemedText>
          فایل طرح‌بندی در <ThemedText type="defaultSemiBold">app/(tabs)/_layout.tsx</ThemedText>{' '}
          ناوبری تب را تنظیم می‌کند.
        </ThemedText>
        <ExternalLink href="https://docs.expo.dev/router/introduction">
          <ThemedText type="link">بیشتر بدانید</ThemedText>
        </ExternalLink>
      </Collapsible>
      <Collapsible title="پشتیبانی از Android، iOS و وب">
        <ThemedText>
          می‌توانید این پروژه را روی Android، iOS و وب باز کنید. برای باز کردن نسخه وب، کلید{' '}
          <ThemedText type="defaultSemiBold">w</ThemedText> را در ترمینال اجرای این پروژه فشار دهید.
        </ThemedText>
      </Collapsible>
      <Collapsible title="تصاویر">
        <ThemedText>
          برای تصاویر ثابت، می‌توانید از پسوندهای <ThemedText type="defaultSemiBold">@2x</ThemedText> و{' '}
          <ThemedText type="defaultSemiBold">@3x</ThemedText> برای ارائه فایل‌هایی با
          تراکم‌های مختلف صفحه استفاده کنید
        </ThemedText>
        <Image source={require('@/assets/images/react-logo.png')} style={{ alignSelf: 'center' }} />
        <ExternalLink href="https://reactnative.dev/docs/images">
          <ThemedText type="link">بیشتر بدانید</ThemedText>
        </ExternalLink>
      </Collapsible>
      <Collapsible title="فونت‌های سفارشی">
        <ThemedText>
          فایل <ThemedText type="defaultSemiBold">app/_layout.tsx</ThemedText> را باز کنید تا ببینید چگونه{' '}
          <ThemedText style={{ fontFamily: 'Vazirmatn' }}>
           فونت دلخواه را بارگذاری کنید!
          </ThemedText>
        </ThemedText>
        <ExternalLink href="https://docs.expo.dev/versions/latest/sdk/font">
          <ThemedText type="link">بیشتر بدانید</ThemedText>
        </ExternalLink>
      </Collapsible>
      <Collapsible title="کامپوننت‌های حالت روشن و تاریک">
        <ThemedText>
          این قالب از حالت روشن و تاریک پشتیبانی می‌کند. هوک{' '}
          <ThemedText type="defaultSemiBold">useColorScheme()</ThemedText> به شما امکان بررسی
          طرح رنگی فعلی کاربر را می‌دهد و بنابراین می‌توانید رنگ‌های رابط کاربری را متناسب تنظیم کنید.
        </ThemedText>
        <ExternalLink href="https://docs.expo.dev/develop/user-interface/color-themes/">
          <ThemedText type="link">بیشتر بدانید</ThemedText>
        </ExternalLink>
      </Collapsible>
      <Collapsible title="انیمیشن‌ها">
        <ThemedText>
          این قالب شامل نمونه‌ای از یک کامپوننت انیمیشنی است. کامپوننت{' '}
          <ThemedText type="defaultSemiBold">components/HelloWave.tsx</ThemedText> از
          کتابخانه قدرتمند <ThemedText type="defaultSemiBold">react-native-reanimated</ThemedText>{' '}
          برای ایجاد انیمیشن دست تکان دادن استفاده می‌کند.
        </ThemedText>
        {Platform.select({
          ios: (
            <ThemedText>
              کامپوننت <ThemedText type="defaultSemiBold">components/ParallaxScrollView.tsx</ThemedText>{' '}
              افکت پارالکس برای تصویر هدر فراهم می‌کند.
            </ThemedText>
          ),
        })}
      </Collapsible>
    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  headerImage: {
    color: '#808080',
    bottom: -90,
    left: -35,
    position: 'absolute',
  },
  titleContainer: {
    flexDirection: 'row',
    gap: 8,
  },
});
