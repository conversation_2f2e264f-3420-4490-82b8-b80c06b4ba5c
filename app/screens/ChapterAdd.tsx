import React, { useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { chapterService } from '@/services/chapterService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { router, useLocalSearchParams } from 'expo-router';

export default function ChapterAddScreen() {
  const { course_id, courseName } = useLocalSearchParams<{ course_id: string, courseName: string }>();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [order_in_course, setOrderInCourse] = useState('');
  const [loading, setLoading] = useState(false);

  const handleAddChapter = async () => {
    if (!name.trim() || !description.trim() || !order_in_course.trim()) {
      Alert.alert('خطای اعتبارسنجی', 'تمامی فیلدها الزامی هستند.');
      return;
    }

    if (!course_id) {
      Alert.alert('خطا', 'شناسه دوره آموزشی یافت نشد.');
      return;
    }

    setLoading(true);
    try {
      await chapterService.createChapter({
        name,
        description,
        order_in_course: parseInt(order_in_course, 10),
        course_id: parseInt(course_id, 10),
      });
      Alert.alert('موفقیت', 'فصل با موفقیت اضافه شد!');
      router.back();
    } catch (error) {
      Alert.alert('خطا', 'فصل اضافه نشد. لطفا دوباره امتحان کنید.');
      console.error('Failed to add chapter:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingWrapper style={styles.container}>
      <ThemedText style={styles.title}>اضافه کردن فصل جدید</ThemedText>
      {courseName && (
        <ThemedText style={styles.courseInfo}>
          دوره: {courseName} (شناسه: {course_id})
        </ThemedText>
      )}
      <TextInput
        style={styles.input}
        placeholder="نام فصل"
        value={name}
        onChangeText={setName}
        editable={!loading}
      />
      <TextInput
        style={styles.input}
        placeholder="توضیحات"
        value={description}
        onChangeText={setDescription}
        editable={!loading}
      />
      <TextInput
        style={styles.input}
        placeholder="ترتیب در دوره"
        value={order_in_course}
        onChangeText={setOrderInCourse}
        editable={!loading}
        keyboardType="numeric"
      />
      <Pressable
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleAddChapter}
        disabled={loading}
      >
        <ThemedText style={styles.buttonText}>
          {loading ? 'در حال اضافه کردن...' : 'اضافه کردن فصل'}
        </ThemedText>
      </Pressable>
    </KeyboardAvoidingWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  courseInfo: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
});
