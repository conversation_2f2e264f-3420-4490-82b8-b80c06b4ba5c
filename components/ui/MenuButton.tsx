import { StyleSheet, TouchableOpacity, useColorScheme } from "react-native";
import { Svg, Path } from "react-native-svg";
import { Colors } from "@/constants/Colors";
import { useNavigation } from "expo-router";

export function MenuButton() {
  const colorScheme = useColorScheme() ?? "light";
  const navigation = useNavigation();
  const openDrawer = () => {
    navigation.openDrawer();
  };

  const MenuIcon = ({ color = "#1F2937", size = 24 }) => (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke={color}
      strokeWidth="2"
    >
      <Path d="M3 12h18M3 6h18M3 18h18" />
    </Svg>
  );
  return (
    <TouchableOpacity style={styles.headerButton} onPress={openDrawer}>
      <MenuIcon color={Colors[colorScheme].text} />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  text: {
    fontSize: 28,
    lineHeight: 32,
    marginTop: -6,
  },
  headerButton: {
    padding: 8,
  },
});
