import { TouchableOpacity, StyleSheet, type ViewProps } from "react-native";
import { ThemedText } from "./ThemedText";

export type ThemedTextProps = ViewProps & {
  lightColor?: string;
  darkColor?: string;
  type?: 'default' | 'title' | 'defaultSemiBold' | 'subtitle' | 'link';
};

export function Card({ title, onPress, style, textStyle, disabled }: ThemedTextProps) {
  return (
    <TouchableOpacity
      style={[styles.button, style, disabled && styles.buttonDisabled]}
      onPress={onPress}
      disabled={disabled}
    >
      <ThemedText style={[styles.buttonText, textStyle]}>{title}</ThemedText>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    backgroundColor: "#4F46E5",
    padding: 15,
    borderRadius: 8,
    alignItems: "center",
  },
  buttonDisabled: { backgroundColor: "#A5B4FC" },
  buttonText: { color: "#FFFFFF", fontSize: 16, fontWeight: "bold" },
});
